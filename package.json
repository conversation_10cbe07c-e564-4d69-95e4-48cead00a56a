{"name": "kambaavoice", "version": "1.0.0", "description": "Voice-to-Text Typing Assistant - Electron app that listens to voice input and auto-types transcribed text", "main": "src/main/main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:3000 && electron .\"", "dev:renderer": "vite", "build": "npm run build:renderer && electron-builder", "build:renderer": "vite build", "dist": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "voice-to-text", "speech-recognition", "typing-assistant", "whisper", "openai"], "author": "KambaVoice Team", "license": "MIT", "devDependencies": {"@vitejs/plugin-react": "^4.2.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "vite": "^5.0.0", "wait-on": "^7.2.0"}, "dependencies": {"autoprefixer": "^10.4.16", "electron-store": "^8.1.0", "lucide-react": "^0.263.1", "node-record-lpcm16": "^1.0.1", "openai": "^4.20.0", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.0", "uiohook-napi": "^1.5.4"}, "build": {"appId": "com.kambaavoice.app", "productName": "KambaVoice", "directories": {"output": "dist"}, "files": ["src/**/*", "dist-renderer/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}