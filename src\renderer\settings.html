<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Settings - KambaVoice</title>
  <style>
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
    }
    
    .settings-container {
      max-width: 500px;
      margin: 0 auto;
    }
    
    h1 {
      text-align: center;
      margin-bottom: 30px;
      font-size: 24px;
    }
    
    .setting-group {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .setting-group h3 {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    .setting-item {
      margin-bottom: 15px;
    }
    
    .setting-item:last-child {
      margin-bottom: 0;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-size: 14px;
      font-weight: 500;
    }
    
    input, select, textarea {
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      font-size: 14px;
    }
    
    input:focus, select:focus, textarea:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
    }
    
    .shortcut-input {
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid transparent;
      transition: border-color 0.2s;
    }
    
    .shortcut-input.recording {
      border-color: #ef4444;
      background: rgba(239, 68, 68, 0.1);
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 30px;
    }
    
    button {
      flex: 1;
      padding: 12px 20px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .btn-primary {
      background: #3b82f6;
      color: white;
    }
    
    .btn-primary:hover {
      background: #2563eb;
    }
    
    .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
    
    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.3);
    }
    
    .help-text {
      font-size: 12px;
      opacity: 0.8;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="settings-container">
    <h1>Settings</h1>
    
    <div class="setting-group">
      <h3>Global Shortcut</h3>
      <div class="setting-item">
        <label for="shortcut">Recording Shortcut</label>
        <input 
          type="text" 
          id="shortcut" 
          class="shortcut-input" 
          value="Ctrl+Shift"
          readonly
          placeholder="Click to record new shortcut"
        >
        <div class="help-text">Click the field above and press your desired key combination</div>
      </div>
    </div>
    
    <div class="setting-group">
      <h3>OpenAI Configuration</h3>
      <div class="setting-item">
        <label for="apiKey">API Key</label>
        <input 
          type="password" 
          id="apiKey" 
          placeholder="Enter your OpenAI API key"
        >
        <div class="help-text">Your API key is stored securely and never shared</div>
      </div>
      <div class="setting-item">
        <label for="model">Transcription Model</label>
        <select id="model">
          <option value="whisper-1">Whisper-1 (Recommended)</option>
          <option value="whisper-1-large">Whisper-1 Large (More Accurate)</option>
        </select>
      </div>
    </div>
    
    <div class="setting-group">
      <h3>Recording Settings</h3>
      <div class="setting-item">
        <label for="language">Language</label>
        <select id="language">
          <option value="auto">Auto-detect</option>
          <option value="en">English</option>
          <option value="es">Spanish</option>
          <option value="fr">French</option>
          <option value="de">German</option>
          <option value="it">Italian</option>
          <option value="pt">Portuguese</option>
          <option value="ru">Russian</option>
          <option value="ja">Japanese</option>
          <option value="ko">Korean</option>
          <option value="zh">Chinese</option>
        </select>
      </div>
      <div class="setting-item">
        <label for="timeout">Recording Timeout (seconds)</label>
        <input 
          type="number" 
          id="timeout" 
          value="30" 
          min="5" 
          max="300"
        >
        <div class="help-text">Maximum recording duration before auto-stop</div>
      </div>
    </div>
    
    <div class="setting-group">
      <h3>Output Settings</h3>
      <div class="setting-item">
        <label for="autoType">Auto-type transcription</label>
        <select id="autoType">
          <option value="true">Yes (Recommended)</option>
          <option value="false">No (Copy to clipboard)</option>
        </select>
      </div>
      <div class="setting-item">
        <label for="typeDelay">Typing Delay (ms)</label>
        <input 
          type="number" 
          id="typeDelay" 
          value="50" 
          min="0" 
          max="1000"
        >
        <div class="help-text">Delay between each character when typing</div>
      </div>
    </div>
    
    <div class="button-group">
      <button type="button" class="btn-secondary" onclick="window.close()">
        Cancel
      </button>
      <button type="button" class="btn-primary" onclick="saveSettings()">
        Save Settings
      </button>
    </div>
  </div>
  
  <script type="module" src="./settings.js"></script>
</body>
</html>
