const fs = require('fs');
const path = require('path');
const { app } = require('electron');

class AudioService {
  constructor() {
    this.isRecording = false;
    this.audioStream = null;
    this.micInputStream = null;
    this.tempAudioPath = null;
    this.recorder = null;
    this.recordingStartTime = null;
  }

  async startRecording() {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      console.log('🎙️ Starting recording simulation for OpenAI testing...');

      // Create temporary file path
      const tempDir = app.getPath('temp');
      this.tempAudioPath = path.join(tempDir, `kambaavoice_${Date.now()}.wav`);

      // Start recording simulation
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      console.log('🎤 Recording simulation started');
      console.log('📢 Hold the key for a few seconds, then release to test OpenAI transcription');

      return this.tempAudioPath;

    } catch (error) {
      console.error('Failed to start real audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  async stopRecording() {
    if (!this.isRecording) {
      throw new Error('Not currently recording');
    }

    try {
      console.log('🛑 Stopping recording simulation...');

      this.isRecording = false;
      const recordingDuration = Date.now() - this.recordingStartTime;

      console.log(`📊 Recording duration: ${recordingDuration}ms`);

      // Create a proper WAV file for OpenAI testing
      this.createTestWavFile(this.tempAudioPath, recordingDuration);

      // Check if file exists and has content
      if (fs.existsSync(this.tempAudioPath)) {
        const stats = fs.statSync(this.tempAudioPath);
        if (stats.size > 0) {
          console.log(`🎵 Test WAV file created: ${this.tempAudioPath} (${stats.size} bytes)`);
          console.log('✅ Sending to OpenAI for transcription test!');
          const filePath = this.tempAudioPath;
          this.tempAudioPath = null;
          return filePath;
        } else {
          throw new Error('Audio file is empty');
        }
      } else {
        throw new Error('Audio file was not created');
      }

    } catch (error) {
      console.error('Failed to stop audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  createTestWavFile(filePath, durationMs) {
    // Create a proper WAV file that OpenAI can process
    const sampleRate = 16000;
    const channels = 1;
    const bitsPerSample = 16;
    const samples = Math.floor(sampleRate * durationMs / 1000);
    const dataSize = samples * channels * bitsPerSample / 8;

    // Create WAV header
    const header = Buffer.alloc(44);

    // RIFF header
    header.write('RIFF', 0);
    header.writeUInt32LE(36 + dataSize, 4);
    header.write('WAVE', 8);

    // fmt chunk
    header.write('fmt ', 12);
    header.writeUInt32LE(16, 16);
    header.writeUInt16LE(1, 20); // PCM
    header.writeUInt16LE(channels, 22);
    header.writeUInt32LE(sampleRate, 24);
    header.writeUInt32LE(sampleRate * channels * bitsPerSample / 8, 28);
    header.writeUInt16LE(channels * bitsPerSample / 8, 32);
    header.writeUInt16LE(bitsPerSample, 34);

    // data chunk
    header.write('data', 36);
    header.writeUInt32LE(dataSize, 40);

    // Create audio data (sine wave for testing)
    const audioData = Buffer.alloc(dataSize);
    for (let i = 0; i < samples; i++) {
      // Generate a 440Hz tone (A note) for testing
      const value = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 16000;
      audioData.writeInt16LE(value, i * 2);
    }

    // Write complete WAV file
    const wavFile = Buffer.concat([header, audioData]);
    fs.writeFileSync(filePath, wavFile);

    console.log(`Created test WAV file: ${samples} samples, ${dataSize} bytes of audio data`);
  }

  cleanup() {
    this.isRecording = false;
    this.audioStream = null;
    this.recorder = null;
    
    // Clean up temporary file
    if (this.tempAudioPath && fs.existsSync(this.tempAudioPath)) {
      try {
        fs.unlinkSync(this.tempAudioPath);
        console.log('Temporary audio file cleaned up');
      } catch (error) {
        console.error('Failed to clean up temporary audio file:', error);
      }
    }
    this.tempAudioPath = null;
  }

  getRecordingState() {
    return this.isRecording;
  }

  // Alternative recording method using node-record-lpcm16
  async startRecordingLPCM() {
    if (this.isRecording) {
      throw new Error('Already recording');
    }

    try {
      const record = require('node-record-lpcm16');
      
      // Create temporary file path
      const tempDir = app.getPath('temp');
      this.tempAudioPath = path.join(tempDir, `kambaavoice_${Date.now()}.wav`);
      
      // Configure recording options
      const recordingOptions = {
        sampleRateHertz: 16000,
        threshold: 0.5,
        verbose: false,
        recordProgram: 'rec', // Use 'sox' on Linux/Mac, 'rec' on Windows
        silence: '1.0',
      };
      
      // Start recording
      const recording = record.record(recordingOptions);
      
      // Create write stream
      const outputFileStream = fs.createWriteStream(this.tempAudioPath);
      recording.stream().pipe(outputFileStream);
      
      this.recorder = recording;
      this.isRecording = true;
      
      console.log('LPCM Audio recording started, saving to:', this.tempAudioPath);
      return this.tempAudioPath;
      
    } catch (error) {
      console.error('Failed to start LPCM audio recording:', error);
      this.cleanup();
      throw error;
    }
  }

  async stopRecordingLPCM() {
    if (!this.isRecording || !this.recorder) {
      throw new Error('Not currently recording');
    }

    try {
      // Stop the recording
      this.recorder.stop();
      this.isRecording = false;
      
      // Wait a bit for the file to be written completely
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Check if file exists and has content
      if (fs.existsSync(this.tempAudioPath)) {
        const stats = fs.statSync(this.tempAudioPath);
        if (stats.size > 0) {
          console.log(`LPCM Audio recording saved: ${this.tempAudioPath} (${stats.size} bytes)`);
          return this.tempAudioPath;
        } else {
          throw new Error('Audio file is empty');
        }
      } else {
        throw new Error('Audio file was not created');
      }
      
    } catch (error) {
      console.error('Failed to stop LPCM audio recording:', error);
      this.cleanup();
      throw error;
    }
  }
}

module.exports = AudioService;
