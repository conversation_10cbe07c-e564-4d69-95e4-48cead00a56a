const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Window management
  showSettings: () => ipcRenderer.invoke('show-settings'),
  
  // Recording controls
  startRecording: () => ipcRenderer.invoke('start-recording'),
  stopRecording: () => ipcRenderer.invoke('stop-recording'),
  getRecordingState: () => ipcRenderer.invoke('get-recording-state'),
  
  // Event listeners
  onRecordingStarted: (callback) => {
    ipcRenderer.on('recording-started', callback);
    return () => ipcRenderer.removeListener('recording-started', callback);
  },
  
  onRecordingStopped: (callback) => {
    ipcRenderer.on('recording-stopped', callback);
    return () => ipcRenderer.removeListener('recording-stopped', callback);
  },
  
  onTranscriptionComplete: (callback) => {
    ipcRenderer.on('transcription-complete', callback);
    return () => ipcRenderer.removeListener('transcription-complete', callback);
  },
  
  // Audio processing
  processAudio: (audioData) => ipcRenderer.invoke('process-audio', audioData),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // History
  getHistory: () => ipcRenderer.invoke('get-history'),
  saveToHistory: (transcription) => ipcRenderer.invoke('save-to-history', transcription),
  deleteFromHistory: (id) => ipcRenderer.invoke('delete-from-history', id),
  exportHistory: () => ipcRenderer.invoke('export-history'),
  
  // System integration
  typeText: (text) => ipcRenderer.invoke('type-text', text),
  testClipboard: (testText) => ipcRenderer.invoke('test-clipboard', testText),

  // Notifications
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body)
});

// Expose Node.js APIs that are safe to use in the renderer
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  versions: process.versions
});
