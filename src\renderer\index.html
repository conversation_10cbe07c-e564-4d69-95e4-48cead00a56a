<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON>Voice - Voice to Text Assistant</title>
  <style>
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .container {
      text-align: center;
      max-width: 600px;
      padding: 40px;
    }

    h1 {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .subtitle {
      font-size: 1.5rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .shortcut {
      background: rgba(255, 255, 255, 0.2);
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-family: monospace;
      margin: 0 0.5rem;
    }

    .record-button {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: #ef4444;
      border: none;
      color: white;
      font-size: 2rem;
      cursor: pointer;
      margin: 2rem 0;
      transition: all 0.3s ease;
    }

    .record-button:hover {
      background: #dc2626;
      transform: scale(1.05);
    }

    .record-button.recording {
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }

    .status {
      font-size: 1.2rem;
      margin-top: 1rem;
    }

    .transcription {
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem;
      border-radius: 8px;
      margin-top: 2rem;
      max-width: 500px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎙️ KambaVoice</h1>
    <p class="subtitle">Voice-to-Text Typing Assistant</p>

    <p>Press <span class="shortcut">Ctrl + Shift + V</span> to start recording</p>

    <button id="recordButton" class="record-button">🎤</button>

    <div style="margin-top: 20px;">
      <button id="settingsButton" style="padding: 10px 20px; background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 8px; cursor: pointer; margin-right: 10px;">⚙️ Settings</button>
      <button id="testClipboardButton" style="padding: 10px 20px; background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 8px; cursor: pointer;">📋 Test Clipboard</button>
    </div>

    <p id="status" class="status">Ready to record</p>

    <div id="transcription" class="transcription" style="display: none;">
      <h3>Last Transcription:</h3>
      <p id="transcriptionText"></p>
    </div>
  </div>

  <script>
    let isRecording = false;
    const recordButton = document.getElementById('recordButton');
    const settingsButton = document.getElementById('settingsButton');
    const testClipboardButton = document.getElementById('testClipboardButton');
    const status = document.getElementById('status');
    const transcriptionDiv = document.getElementById('transcription');
    const transcriptionText = document.getElementById('transcriptionText');

    // Button click handlers
    recordButton.addEventListener('click', async () => {
      if (!isRecording) {
        await startRecording();
      } else {
        await stopRecording();
      }
    });

    settingsButton.addEventListener('click', async () => {
      if (window.electronAPI && window.electronAPI.showSettings) {
        await window.electronAPI.showSettings();
      } else {
        alert('Settings functionality not available in demo mode');
      }
    });

    testClipboardButton.addEventListener('click', async () => {
      const testText = `Hello! This is a clipboard test from KambaVoice at ${new Date().toLocaleTimeString()}. If you can paste this text, the clipboard is working correctly! 🎉`;

      if (window.electronAPI && window.electronAPI.testClipboard) {
        await window.electronAPI.testClipboard(testText);
      } else {
        // Fallback for demo mode
        try {
          await navigator.clipboard.writeText(testText);
          alert('Test text copied to clipboard! Try pasting with Ctrl+V');
        } catch (e) {
          alert('Clipboard test failed. Your browser may not support clipboard access.');
        }
      }
    });

    async function startRecording() {
      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.startRecording();
          if (result.success) {
            isRecording = true;
            recordButton.classList.add('recording');
            recordButton.textContent = '⏹️';
            status.textContent = 'Recording... Click to stop';
          }
        } else {
          // Demo mode
          isRecording = true;
          recordButton.classList.add('recording');
          recordButton.textContent = '⏹️';
          status.textContent = 'Recording... (Demo mode)';
        }
      } catch (error) {
        console.error('Failed to start recording:', error);
        status.textContent = 'Error: ' + error.message;
      }
    }

    async function stopRecording() {
      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.stopRecording();
          if (result.success) {
            isRecording = false;
            recordButton.classList.remove('recording');
            recordButton.textContent = '🎤';
            status.textContent = 'Processing...';
          }
        } else {
          // Demo mode
          isRecording = false;
          recordButton.classList.remove('recording');
          recordButton.textContent = '🎤';
          status.textContent = 'Ready to record';

          // Show demo transcription
          setTimeout(() => {
            showTranscription('This is a demo transcription. Configure your OpenAI API key in settings for real transcription.');
          }, 1000);
        }
      } catch (error) {
        console.error('Failed to stop recording:', error);
        status.textContent = 'Error: ' + error.message;
      }
    }

    function showTranscription(text) {
      transcriptionText.textContent = text;
      transcriptionDiv.style.display = 'block';
      status.textContent = 'Transcription complete!';
    }

    // Listen for events from main process
    if (window.electronAPI) {
      window.electronAPI.onRecordingStarted(() => {
        isRecording = true;
        recordButton.classList.add('recording');
        recordButton.textContent = '⏹️';
        status.textContent = 'Recording via shortcut...';
      });

      window.electronAPI.onRecordingStopped(() => {
        isRecording = false;
        recordButton.classList.remove('recording');
        recordButton.textContent = '🎤';
        status.textContent = 'Processing...';
      });

      window.electronAPI.onTranscriptionComplete((event, transcription) => {
        showTranscription(transcription.text);
      });
    }

    console.log('KambaVoice UI loaded');
  </script>
</body>
</html>
