{"name": "mic", "version": "2.1.2", "description": "A simple stream wrapper for arecord (Linux (including Raspbian)) and sox (Mac/Windows). Returns a Passthrough stream object so that stream control like pause(), resume(), pipe(), etc. are all available.", "main": "index.js", "scripts": {"test": "node tests/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/ashishbajaj99/mic.git"}, "keywords": ["node-mic", "microphone", "sox", "alsa", "arecord", "mic", "audio", "input"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ashishbajaj99/mic/issues"}, "homepage": "https://github.com/ashishbajaj99/mic#readme"}