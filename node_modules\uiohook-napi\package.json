{"name": "uiohook-napi", "version": "1.5.4", "author": {"name": "<PERSON>"}, "repository": {"type": "git", "url": "https://github.com/SnosMe/uiohook-napi.git"}, "license": "MIT", "keywords": ["iohook", "uiohook", "libui<PERSON><PERSON>", "hook", "input", "keyboard", "mouse"], "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"install": "node-gyp-build", "prebuild": "prebuildify --napi", "build-ts": "tsc", "demo": "ts-node src/demo.ts", "make-libuiohook-patch": "git -C ./libuiohook diff --cached > ./src/libuiohook.patch", "apply-libuiohook-patch": "git -C ./libuiohook apply ../src/libuiohook.patch"}, "files": ["dist", "binding.gyp", "libuiohook/src", "libuiohook/include", "src/lib", "prebuilds"], "devDependencies": {"@types/node": "18.x.x", "prebuildify": "5.x.x", "ts-node": "10.x.x", "typescript": "4.x.x"}, "dependencies": {"node-gyp-build": "4.x.x"}, "prebuild": {"test": "dist/prebuild-test-noop.js"}, "gypfile": true, "engines": {"node": ">= 16"}}