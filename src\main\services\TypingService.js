const { clipboard } = require('electron');

class TypingService {
  constructor() {
    this.robot = null;
    this.keyboardSender = null;
    this.initializeTypingLibraries();
  }

  initializeTypingLibraries() {
    console.log('Initializing typing libraries...');

    // For now, we'll use clipboard-based typing as the primary method
    // This is more reliable across different systems
    console.log('Using clipboard-based typing method');

    // Note: In a production app, you would want to install and configure
    // native typing libraries like robotjs or use system-specific APIs
  }

  async typeText(text, options = {}) {
    const {
      delay = 50,           // Delay between characters in milliseconds
      useClipboard = false, // Whether to use clipboard paste instead of typing
      preserveFormatting = true
    } = options;

    if (!text || typeof text !== 'string') {
      throw new Error('Invalid text provided for typing');
    }

    try {
      // For now, always use clipboard method as it's most reliable
      console.log('Typing text via clipboard:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));
      return await this.pasteFromClipboard(text);
    } catch (error) {
      console.error('Failed to type text:', error);
      throw error;
    }
  }

  async typeCharacterBy<PERSON>haracter(text, delay, preserveFormatting) {
    if (this.robot) {
      return await this.typeWithRobotJS(text, delay, preserveFormatting);
    } else if (this.keyboardSender) {
      return await this.typeWithKeyboardSender(text, delay);
    } else {
      throw new Error('No typing library available');
    }
  }

  async typeWithRobotJS(text, delay, preserveFormatting) {
    console.log('Typing with RobotJS:', text.substring(0, 50) + '...');
    
    // Small delay to ensure the target window is ready
    await this.sleep(100);
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      
      try {
        if (char === '\n') {
          this.robot.keyTap('enter');
        } else if (char === '\t') {
          this.robot.keyTap('tab');
        } else {
          this.robot.typeString(char);
        }
        
        if (delay > 0) {
          await this.sleep(delay);
        }
      } catch (charError) {
        console.warn(`Failed to type character '${char}':`, charError.message);
        // Continue with next character
      }
    }
    
    console.log('Typing completed with RobotJS');
    return { success: true, method: 'robotjs' };
  }

  async typeWithKeyboardSender(text, delay) {
    console.log('Typing with node-key-sender:', text.substring(0, 50) + '...');
    
    // Small delay to ensure the target window is ready
    await this.sleep(100);
    
    try {
      // node-key-sender can send the entire string at once
      await this.keyboardSender.sendText(text);
      
      console.log('Typing completed with node-key-sender');
      return { success: true, method: 'node-key-sender' };
    } catch (error) {
      console.error('node-key-sender failed:', error);
      throw error;
    }
  }

  async pasteFromClipboard(text) {
    console.log('Using clipboard method for text input');

    try {
      // Store current clipboard content
      const originalClipboard = clipboard.readText();

      // Set our text to clipboard
      clipboard.writeText(text);

      // Small delay to ensure clipboard is updated
      await this.sleep(50);

      // Show notification with the transcribed text
      try {
        const { Notification } = require('electron');
        if (Notification.isSupported()) {
          const notification = new Notification({
            title: '🎙️ KambaVoice - Text Ready!',
            body: `"${text.substring(0, 80)}${text.length > 80 ? '...' : ''}"\n\n📋 Press Ctrl+V to paste anywhere!`,
            silent: false,
            timeoutType: 'default'
          });

          notification.show();
          console.log('Notification shown with transcribed text');

          // Auto-close notification after 8 seconds
          setTimeout(() => {
            try {
              notification.close();
            } catch (e) {
              // Notification might already be closed
            }
          }, 8000);
        }
      } catch (notificationError) {
        console.error('Failed to show notification:', notificationError);
      }

      // For now, we'll just put the text in clipboard and notify the user
      // In a real implementation, you would send Ctrl+V programmatically
      console.log('Text copied to clipboard. User should paste manually with Ctrl+V');

      // Wait a bit before restoring clipboard
      await this.sleep(2000);

      // Restore original clipboard content
      clipboard.writeText(originalClipboard);

      console.log('Clipboard operation completed');
      return { success: true, method: 'clipboard', note: 'Text copied to clipboard - paste manually' };

    } catch (error) {
      console.error('Clipboard operation failed:', error);
      throw error;
    }
  }

  async sendKeyboardShortcut(keys) {
    if (!Array.isArray(keys) || keys.length === 0) {
      throw new Error('Invalid keyboard shortcut');
    }

    try {
      if (this.robot) {
        // RobotJS expects modifiers as an array and the main key separately
        const mainKey = keys[keys.length - 1];
        const modifiers = keys.slice(0, -1);
        this.robot.keyTap(mainKey.toLowerCase(), modifiers.map(k => k.toLowerCase()));
      } else if (this.keyboardSender) {
        await this.keyboardSender.sendCombination(keys.map(k => k.toLowerCase()));
      } else {
        throw new Error('No keyboard library available');
      }
      
      return { success: true };
    } catch (error) {
      console.error('Failed to send keyboard shortcut:', error);
      throw error;
    }
  }

  async getActiveWindow() {
    if (this.robot) {
      try {
        const activeWindow = this.robot.getActiveWindow();
        return {
          title: activeWindow.title,
          bounds: activeWindow.bounds,
          pid: activeWindow.pid
        };
      } catch (error) {
        console.error('Failed to get active window:', error);
        return null;
      }
    }
    return null;
  }

  async focusWindow(windowTitle) {
    // This is a placeholder - actual implementation would depend on the OS
    console.log('Focus window not implemented yet:', windowTitle);
    return false;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Test typing functionality
  async testTyping() {
    const testText = "Hello, this is a test from KambaVoice!";
    
    try {
      console.log('Testing typing functionality...');
      await this.sleep(2000); // Give user time to focus a text field
      
      const result = await this.typeText(testText, { delay: 100 });
      console.log('Typing test completed:', result);
      return result;
    } catch (error) {
      console.error('Typing test failed:', error);
      throw error;
    }
  }
}

module.exports = TypingService;
