const { app, BrowserWindow, Tray, Menu, ipcMain, globalShortcut, dialog, Notification } = require('electron');
const path = require('path');
const { uIOhook, UiohookKey } = require('uiohook-napi');
const isDev = process.env.NODE_ENV === 'development';

// Import services
const AudioService = require('./services/AudioService');
const TranscriptionService = require('./services/TranscriptionService');
const TypingService = require('./services/TypingService');
const SettingsService = require('./services/SettingsService');

class KambaVoiceApp {
  constructor() {
    this.mainWindow = null;
    this.settingsWindow = null;
    this.recordingWidget = null;
    this.tray = null;
    this.isRecording = false;
    this.isKeyPressed = false;

    // Initialize services
    this.audioService = new AudioService();
    this.transcriptionService = new TranscriptionService();
    this.typingService = new TypingService();
    this.settingsService = new SettingsService();

    // Add debouncing for shortcuts
    this.lastShortcutTime = 0;

    this.initializeApp();
  }

  initializeApp() {
    // Handle app ready
    app.whenReady().then(() => {
      // Don't show main window on startup - only create overlay widget when needed
      this.registerGlobalShortcuts();
      this.setupIpcHandlers();

      // Create system tray for access to dashboard
      this.createSystemTray();

      // On macOS, re-create window when dock icon is clicked
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Quit when all windows are closed (except on macOS)
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app before quit
    app.on('before-quit', () => {
      this.cleanup();
    });
  }

  createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false, // Hidden by default - only show when requested
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    // Load the renderer - dashboard for settings and logs
    this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    // Don't auto-open dev tools

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Hide instead of closing (keep app running in background)
    this.mainWindow.on('close', (event) => {
      event.preventDefault();
      this.mainWindow.hide();
    });
  }

  createSettingsWindow() {
    if (this.settingsWindow) {
      this.settingsWindow.focus();
      return;
    }

    this.settingsWindow = new BrowserWindow({
      width: 600,
      height: 500,
      resizable: false,
      parent: this.mainWindow,
      modal: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    if (isDev) {
      this.settingsWindow.loadURL('http://localhost:3000/settings');
    } else {
      this.settingsWindow.loadFile(path.join(__dirname, '../renderer/settings.html'));
    }

    this.settingsWindow.on('closed', () => {
      this.settingsWindow = null;
    });
  }

  createRecordingWindow() {
    this.recordingWindow = new BrowserWindow({
      width: 300,
      height: 80,
      frame: false,
      alwaysOnTop: true,
      transparent: true,
      resizable: false,
      skipTaskbar: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    // Center the window at the top of the screen
    const { screen } = require('electron');
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width } = primaryDisplay.workAreaSize;
    this.recordingWindow.setPosition(Math.round((width - 300) / 2), 50);

    if (isDev) {
      this.recordingWindow.loadURL('http://localhost:3000/recording');
    } else {
      this.recordingWindow.loadFile(path.join(__dirname, '../renderer/recording.html'));
    }

    this.recordingWindow.on('closed', () => {
      this.recordingWindow = null;
    });
  }

  showRecordingWidget() {
    if (this.recordingWidget) {
      this.recordingWidget.show();
      return;
    }

    // Create small recording widget
    this.recordingWidget = new BrowserWindow({
      width: 200,
      height: 80,
      frame: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: false,
      transparent: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../shared/preload.js')
      }
    });

    // Position widget at top-right of screen
    const { screen } = require('electron');
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;
    this.recordingWidget.setPosition(width - 220, 20);

    // Load widget HTML
    this.recordingWidget.loadFile(path.join(__dirname, '../renderer/widget.html'));

    this.recordingWidget.on('closed', () => {
      this.recordingWidget = null;
    });
  }

  hideRecordingWidget() {
    if (this.recordingWidget) {
      this.recordingWidget.close();
      this.recordingWidget = null;
    }
  }

  createSystemTray() {
    // Simple system tray for accessing dashboard
    const { Menu, Tray } = require('electron');

    // For now, just create a simple menu without icon
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show Dashboard',
        click: () => {
          this.showDashboard();
        }
      },
      {
        label: 'Settings',
        click: () => {
          this.createSettingsWindow();
        }
      },
      { type: 'separator' },
      {
        label: 'Quit KambaVoice',
        click: () => {
          app.quit();
        }
      }
    ]);

    // Note: We'll skip tray icon for now to avoid icon issues
    console.log('System tray menu created (no icon)');
  }

  showDashboard() {
    if (!this.mainWindow) {
      this.createMainWindow();
    }
    this.mainWindow.show();
    this.mainWindow.focus();
  }

  showRecordingNotification(message) {
    try {
      const { Notification } = require('electron');
      if (Notification.isSupported()) {
        const notification = new Notification({
          title: 'KambaVoice',
          body: message,
          silent: false,
          timeoutType: 'default'
        });

        notification.show();

        // Auto-close after 3 seconds
        setTimeout(() => {
          try {
            notification.close();
          } catch (e) {
            // Notification might already be closed
          }
        }, 3000);
      }
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }

  registerGlobalShortcuts() {
    try {
      // Try to use proper press-and-hold detection first
      this.setupPressAndHoldDetection();
      console.log('Press-and-hold shortcut registered with uIOhook');
    } catch (error) {
      console.error('Failed to set up uIOhook, falling back to simple toggle:', error);
      // Fallback to simple toggle if uIOhook fails
      this.setupToggleFallback();
      console.log('Simple toggle shortcut registered as fallback');
    }
  }

  setupPressAndHoldDetection() {
    let ctrlPressed = false;
    let backtickPressed = false;
    let recordingStarted = false;

    // Track key states
    uIOhook.on('keydown', (e) => {
      if (e.keycode === UiohookKey.Ctrl) ctrlPressed = true;
      if (e.keycode === UiohookKey.Backquote) backtickPressed = true; // Backtick key

      // Check if both keys are pressed and we haven't started recording yet
      if (ctrlPressed && backtickPressed && !this.isRecording && !recordingStarted) {
        recordingStarted = true;
        console.log('🎙️ PRESS-AND-HOLD: Ctrl+` pressed, starting recording...');
        this.startPressAndHoldRecording();
      }
    });

    uIOhook.on('keyup', (e) => {
      if (e.keycode === UiohookKey.Ctrl) ctrlPressed = false;
      if (e.keycode === UiohookKey.Backquote) backtickPressed = false;

      // Stop recording when any of the required keys is released (only if we're recording)
      if (recordingStarted && this.isRecording && (!ctrlPressed || !backtickPressed)) {
        console.log('🛑 PRESS-AND-HOLD: Key released, stopping recording...');
        recordingStarted = false;
        this.stopPressAndHoldRecording();
      }
    });

    // Start the hook
    uIOhook.start();
  }

  setupToggleFallback() {
    // Fallback to a simpler press-and-hold simulation
    const shortcut = 'CommandOrControl+`';

    globalShortcut.register(shortcut, () => {
      this.handleSimplePressAndHold();
    });

    console.log(`Fallback: Simple press-and-hold shortcut ${shortcut} registered`);
  }

  handleSimplePressAndHold() {
    if (!this.isRecording) {
      // Start recording
      console.log('🎙️ SHORTCUT: Starting recording...');
      this.startPressAndHoldRecording();

      // Set up auto-stop after reasonable time (10 seconds max)
      this.autoStopTimer = setTimeout(() => {
        if (this.isRecording) {
          console.log('⏰ Auto-stopping recording after 10 seconds');
          this.stopPressAndHoldRecording();
        }
      }, 10000);

    } else {
      // Stop recording if it's been at least 0.5 seconds
      const now = Date.now();
      if (this.recordingStartTime && (now - this.recordingStartTime) > 500) {
        console.log('🛑 SHORTCUT: Stopping recording...');
        this.stopPressAndHoldRecording();
      } else {
        console.log('⚠️ Recording too short, keep holding...');
      }
    }
  }

  handleToggleRecording() {
    // Add debouncing to prevent rapid triggering
    const now = Date.now();
    if (this.lastShortcutTime && (now - this.lastShortcutTime) < 500) {
      console.log('Shortcut triggered too quickly, ignoring...');
      return;
    }
    this.lastShortcutTime = now;

    if (!this.isRecording) {
      // Start recording
      console.log('🎙️ RECORDING STARTED - Speak now! Press Ctrl+` again to stop.');
      this.startPushToTalkRecording();

      // Show notification that recording started
      this.showRecordingNotification('🎙️ Recording... Press Ctrl+` again to stop');

      // Set up auto-stop timer (max recording duration - 15 seconds)
      this.autoStopTimer = setTimeout(() => {
        if (this.isRecording) {
          console.log('Auto-stopping recording after 15 seconds');
          this.stopPushToTalkRecording();
        }
      }, 15000); // Max 15 seconds recording

    } else {
      // Stop recording
      console.log('🛑 RECORDING STOPPED - Processing your speech...');
      this.stopPushToTalkRecording();
    }
  }

  async startRecording() {
    if (this.isRecording) {
      console.log('Already recording');
      return { success: false, message: 'Already recording' };
    }

    try {
      console.log('Starting voice recording...');

      // Set up transcription service with current API key if available
      const openAISettings = this.settingsService.getOpenAISettings();
      if (openAISettings.apiKey) {
        this.transcriptionService.setApiKey(openAISettings.apiKey);
      }

      // Start audio recording
      const audioPath = await this.audioService.startRecording();
      this.isRecording = true;

      // Create and show recording window
      this.createRecordingWindow();

      // Notify renderer processes
      if (this.mainWindow) {
        this.mainWindow.webContents.send('recording-started');
      }
      if (this.recordingWindow) {
        this.recordingWindow.webContents.send('recording-started');
      }

      return { success: true, audioPath };
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.isRecording = false;
      return { success: false, message: error.message };
    }
  }

  async stopRecording() {
    if (!this.isRecording) {
      console.log('Not currently recording');
      return { success: false, message: 'Not currently recording' };
    }

    try {
      console.log('Stopping voice recording...');

      // Stop audio recording and get file path
      const audioPath = await this.audioService.stopRecording();
      this.isRecording = false;

      // Hide recording window
      if (this.recordingWindow) {
        this.recordingWindow.close();
      }

      // Notify renderer processes
      if (this.mainWindow) {
        this.mainWindow.webContents.send('recording-stopped');
      }

      // Process the audio for transcription
      this.processRecordedAudio(audioPath).catch(error => {
        console.error('Audio processing failed:', error);
        // Show error notification
        if (this.settingsService.getSetting('showNotifications')) {
          const { Notification } = require('electron');
          new Notification({
            title: 'Transcription Failed',
            body: 'Failed to process audio: ' + error.message
          }).show();
        }
      });

      return { success: true, audioPath };
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.isRecording = false;
      return { success: false, message: error.message };
    }
  }

  async startPressAndHoldRecording() {
    if (this.isRecording) {
      return;
    }

    try {
      console.log('Starting press-and-hold recording...');
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // Show recording widget
      this.showRecordingWidget();

      // Show notification
      this.showRecordingNotification('🎙️ Recording... Release Ctrl+` to stop and transcribe');

      // Set up transcription service
      const openAISettings = this.settingsService.getOpenAISettings();
      if (openAISettings.apiKey) {
        this.transcriptionService.setApiKey(openAISettings.apiKey);
      }

      // Start audio recording
      this.currentAudioPath = await this.audioService.startRecording();
      console.log('Press-and-hold recording started');

    } catch (error) {
      console.error('Failed to start press-and-hold recording:', error);
      this.isRecording = false;
      this.recordingStartTime = null;
      this.hideRecordingWidget();
    }
  }

  async stopPressAndHoldRecording() {
    if (!this.isRecording) {
      return;
    }

    try {
      console.log('Stopping press-and-hold recording...');

      // Reset recording state
      this.recordingStartTime = null;

      // Stop audio recording and get file path
      const audioPath = await this.audioService.stopRecording();
      this.isRecording = false;

      // Hide recording widget
      this.hideRecordingWidget();

      // Process the audio for transcription and auto-paste
      this.processRecordedAudioAndPaste(audioPath).catch(error => {
        console.error('Audio processing failed:', error);
      });

    } catch (error) {
      console.error('Failed to stop press-and-hold recording:', error);
      this.isRecording = false;
      this.recordingStartTime = null;
      this.hideRecordingWidget();
    }
  }

  async startPushToTalkRecording() {
    if (this.isRecording) {
      console.log('Already recording');
      return;
    }

    try {
      console.log('Starting push-to-talk recording...');
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // Show recording widget
      this.showRecordingWidget();

      // Set up transcription service with current API key if available
      const openAISettings = this.settingsService.getOpenAISettings();
      if (openAISettings.apiKey) {
        this.transcriptionService.setApiKey(openAISettings.apiKey);
      }

      // Start audio recording
      this.currentAudioPath = await this.audioService.startRecording();
      console.log('Push-to-talk recording started');

    } catch (error) {
      console.error('Failed to start push-to-talk recording:', error);
      this.isRecording = false;
      this.recordingStartTime = null;
      this.hideRecordingWidget();
      throw error;
    }
  }

  async stopPushToTalkRecording() {
    if (!this.isRecording) {
      console.log('Not currently recording');
      return;
    }

    try {
      console.log('Stopping push-to-talk recording...');

      // Clear the auto-stop timer
      if (this.autoStopTimer) {
        clearTimeout(this.autoStopTimer);
        this.autoStopTimer = null;
      }

      // Reset recording state
      this.recordingStartTime = null;

      // Stop audio recording and get file path
      const audioPath = await this.audioService.stopRecording();
      this.isRecording = false;

      // Hide recording widget
      this.hideRecordingWidget();

      // Process the audio for transcription and auto-paste
      this.processRecordedAudioAndPaste(audioPath).catch(error => {
        console.error('Audio processing failed:', error);
      });

    } catch (error) {
      console.error('Failed to stop push-to-talk recording:', error);
      this.isRecording = false;
      this.recordingStartTime = null;
      this.hideRecordingWidget();
    }
  }

  setupIpcHandlers() {
    // App info
    ipcMain.handle('get-app-version', () => {
      return app.getVersion();
    });

    // Window management
    ipcMain.handle('show-settings', () => {
      this.createSettingsWindow();
    });

    // Recording controls
    ipcMain.handle('start-recording', async () => {
      return await this.startRecording();
    });

    ipcMain.handle('stop-recording', async () => {
      return await this.stopRecording();
    });

    ipcMain.handle('get-recording-state', () => {
      return this.isRecording;
    });

    // Audio processing
    ipcMain.handle('process-audio', async (event, audioData) => {
      return await this.processAudio(audioData);
    });

    // Settings
    ipcMain.handle('get-settings', () => {
      return this.settingsService.getSettings();
    });

    ipcMain.handle('save-settings', async (event, settings) => {
      return await this.saveSettings(settings);
    });

    // Text typing
    ipcMain.handle('type-text', async (event, text) => {
      const typingSettings = this.settingsService.getTypingSettings();
      return await this.typingService.typeText(text, typingSettings);
    });

    // Test clipboard functionality
    ipcMain.handle('test-clipboard', async (event, testText) => {
      try {
        console.log('Testing clipboard with text:', testText.substring(0, 50) + '...');
        await this.typingService.typeText(testText);
        return { success: true, message: 'Clipboard test completed!' };
      } catch (error) {
        console.error('Clipboard test failed:', error);
        return { success: false, error: error.message };
      }
    });

    // Notifications
    ipcMain.handle('show-notification', (event, title, body) => {
      if (this.settingsService.getSetting('showNotifications')) {
        new Notification({ title, body }).show();
      }
    });

    // History management (placeholder for now)
    ipcMain.handle('get-history', () => {
      return []; // TODO: Implement history storage
    });

    ipcMain.handle('save-to-history', (event, transcription) => {
      // TODO: Implement history storage
      return true;
    });

    ipcMain.handle('delete-from-history', (event, id) => {
      // TODO: Implement history storage
      return true;
    });

    ipcMain.handle('export-history', () => {
      // TODO: Implement history export
      return true;
    });
  }

  async processRecordedAudio(audioPath) {
    try {
      console.log('Processing recorded audio:', audioPath);

      // Get transcription settings
      const openAISettings = this.settingsService.getOpenAISettings();

      // Transcribe the audio
      const transcription = await this.transcriptionService.transcribeAudio(audioPath, {
        model: openAISettings.model,
        language: openAISettings.language === 'auto' ? undefined : openAISettings.language
      });

      console.log('Transcription completed:', transcription.text);

      // Enhance with GPT if enabled
      let finalText = transcription.text;
      if (openAISettings.enhanceWithGPT && transcription.text.length > 0) {
        try {
          finalText = await this.transcriptionService.enhanceTranscription(transcription.text);
        } catch (enhanceError) {
          console.warn('Enhancement failed, using original transcription:', enhanceError);
        }
      }

      // Type the text if auto-typing is enabled
      const typingSettings = this.settingsService.getTypingSettings();
      if (typingSettings.autoType && finalText.length > 0) {
        await this.typingService.typeText(finalText, typingSettings);
      }

      // Show notification
      if (this.settingsService.getSetting('showNotifications')) {
        new Notification({
          title: 'Transcription Complete',
          body: finalText.length > 50 ? finalText.substring(0, 50) + '...' : finalText
        }).show();
      }

      // Send transcription to renderer processes
      const transcriptionData = {
        ...transcription,
        text: finalText,
        enhanced: openAISettings.enhanceWithGPT
      };

      if (this.mainWindow) {
        this.mainWindow.webContents.send('transcription-complete', transcriptionData);
      }

      return transcriptionData;
    } catch (error) {
      console.error('Audio processing failed:', error);

      // Show error notification
      if (this.settingsService.getSetting('showNotifications')) {
        new Notification({
          title: 'Transcription Failed',
          body: error.message
        }).show();
      }

      throw error;
    }
  }

  async processRecordedAudioAndPaste(audioPath) {
    try {
      console.log('Processing recorded audio for auto-paste:', audioPath);

      // Transcribe the audio
      const transcription = await this.transcriptionService.transcribeAudio(audioPath);
      console.log('Transcription completed:', transcription.text);

      // Auto-paste the transcribed text at cursor location
      await this.autoPasteText(transcription.text);

      return { success: true, transcription };

    } catch (error) {
      console.error('Audio processing failed:', error);

      // Show error notification
      if (this.settingsService.getSetting('showNotifications')) {
        const { Notification } = require('electron');
        new Notification({
          title: 'KambaVoice Error',
          body: 'Failed to transcribe audio: ' + error.message
        }).show();
      }

      throw error;
    }
  }

  async autoPasteText(text) {
    try {
      console.log('Auto-pasting text:', text.substring(0, 50) + '...');

      // Use the TypingService which handles clipboard-based typing
      const typingSettings = this.settingsService.getTypingSettings();
      const result = await this.typingService.typeText(text, typingSettings);

      console.log('Text auto-pasted successfully:', result);

      return result;

    } catch (error) {
      console.error('Auto-paste failed:', error);

      // Fallback to manual clipboard
      const { clipboard } = require('electron');
      clipboard.writeText(text);

      if (this.settingsService.getSetting('showNotifications')) {
        const { Notification } = require('electron');
        new Notification({
          title: '🎙️ KambaVoice',
          body: `Text copied to clipboard. Press Ctrl+V to paste: "${text.substring(0, 40)}..."`
        }).show();
      }

      throw error;
    }
  }

  async saveSettings(settings) {
    try {
      // Validate settings
      const validation = this.settingsService.validateSettings(settings);
      if (!validation.valid) {
        throw new Error(`Invalid settings: ${validation.errors.join(', ')}`);
      }

      // Save settings
      this.settingsService.setSettings(settings);

      // Update global shortcut if it changed
      const currentShortcut = this.settingsService.getSetting('shortcut');
      if (settings.shortcut && settings.shortcut !== currentShortcut) {
        this.updateGlobalShortcut(settings.shortcut);
      }

      // Update transcription service API key if it changed
      if (settings.apiKey) {
        this.transcriptionService.setApiKey(settings.apiKey);
      }

      console.log('Settings saved successfully');
      return { success: true };
    } catch (error) {
      console.error('Failed to save settings:', error);
      return { success: false, message: error.message };
    }
  }

  updateGlobalShortcut(newShortcut) {
    try {
      // Unregister current shortcut
      globalShortcut.unregisterAll();

      // Register new shortcut
      globalShortcut.register(newShortcut, () => {
        this.handleVoiceShortcut();
      });

      console.log(`Global shortcut updated to: ${newShortcut}`);
      return true;
    } catch (error) {
      console.error('Failed to update global shortcut:', error);
      // Fallback to default shortcut
      this.registerGlobalShortcuts();
      return false;
    }
  }

  // Icon creation removed for simplicity

  cleanup() {
    // Cleanup services
    if (this.audioService) {
      this.audioService.cleanup();
    }

    // Unregister global shortcuts
    globalShortcut.unregisterAll();

    // Stop uIOhook
    try {
      uIOhook.stop();
    } catch (error) {
      console.error('Failed to stop uIOhook:', error);
    }

    // Close all windows
    if (this.mainWindow) this.mainWindow.destroy();
    if (this.settingsWindow) this.settingsWindow.destroy();
    if (this.recordingWindow) this.recordingWindow.destroy();

    // Tray removed for simplicity
  }
}

// Create the app instance
new KambaVoiceApp();
