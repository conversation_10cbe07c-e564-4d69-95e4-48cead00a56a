<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KambaVoice Recording</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      border-radius: 12px;
      overflow: hidden;
      user-select: none;
      -webkit-app-region: drag;
    }
    
    .widget-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15px;
      height: 50px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    .recording-indicator {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .pulse-dot {
      width: 12px;
      height: 12px;
      background: white;
      border-radius: 50%;
      animation: pulse 1s infinite;
    }
    
    @keyframes pulse {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.2); }
      100% { opacity: 1; transform: scale(1); }
    }
    
    .recording-text {
      font-size: 14px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
    
    .processing {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    .processing .pulse-dot {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="widget-container" id="widgetContainer">
    <div class="recording-indicator">
      <div class="pulse-dot"></div>
      <div class="recording-text" id="statusText">Recording...</div>
    </div>
  </div>

  <script>
    let isRecording = true;
    
    function updateStatus(status) {
      const container = document.getElementById('widgetContainer');
      const statusText = document.getElementById('statusText');
      
      if (status === 'recording') {
        container.className = 'widget-container';
        statusText.textContent = 'Recording...';
      } else if (status === 'processing') {
        container.className = 'widget-container processing';
        statusText.textContent = 'Processing...';
      } else if (status === 'complete') {
        container.className = 'widget-container processing';
        statusText.textContent = 'Complete!';
        
        // Auto-close after 1 second
        setTimeout(() => {
          if (window.electronAPI && window.electronAPI.closeWidget) {
            window.electronAPI.closeWidget();
          }
        }, 1000);
      }
    }
    
    // Listen for status updates from main process
    if (window.electronAPI) {
      window.electronAPI.onWidgetStatusUpdate((event, status) => {
        updateStatus(status);
      });
    }
    
    console.log('Recording widget loaded');
  </script>
</body>
</html>
