const fs = require('fs');
const OpenAI = require('openai');

class TranscriptionService {
  constructor() {
    this.openai = null;
    this.apiKey = null;
  }
  

  setApiKey(apiKey) {
    this.apiKey = apiKey;
    this.openai = new OpenAI({
      apiKey: apiKey
    });
  }

  async transcribeAudio(audioFilePath, options = {}) {
    if (!fs.existsSync(audioFilePath)) {
      throw new Error('Audio file does not exist');
    }

    try {
      console.log('Starting transcription for:', audioFilePath);

      // Check if OpenAI API key is configured
      if (!this.openai) {
        console.log('Using demo transcription (no API key configured)');

        // Clean up the temporary audio file
        try {
          fs.unlinkSync(audioFilePath);
          console.log('Temporary audio file cleaned up');
        } catch (cleanupError) {
          console.error('Failed to clean up audio file:', cleanupError);
        }

        return {
          text: "Please configure your OpenAI API key in settings to enable voice transcription.",
          timestamp: new Date().toISOString(),
          model: options.model || 'whisper-1',
          language: options.language || 'auto',
          demo: true
        };
      }

      // Real OpenAI Whisper transcription
      console.log('🎯 Transcribing with OpenAI Whisper...');
      console.log('API Key configured:', !!this.apiKey);
      console.log('OpenAI client initialized:', !!this.openai);

      // Check file size
      const stats = fs.statSync(audioFilePath);
      console.log('Audio file size:', stats.size, 'bytes');

      console.log('Creating transcription request...');

      let transcription;
      try {
        console.log('Sending audio to OpenAI Whisper...');

        // Add timeout to prevent hanging
        const apiCall = this.openai.audio.transcriptions.create({
          file: fs.createReadStream(audioFilePath),
          model: 'whisper-1'
        });

        const timeout = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('API timeout after 15 seconds')), 15000);
        });

        console.log('Waiting for API response...');
        const response = await Promise.race([apiCall, timeout]);

        transcription = response;
        console.log('✅ Transcription completed!');
        console.log('📝 Result:', transcription);

      } catch (error) {
        console.error('❌ OpenAI API Error:', error.message);

        if (error.message.includes('timeout')) {
          transcription = `[API Timeout] The OpenAI API is not responding. This might be due to network issues or the audio file format. Audio size: ${stats.size} bytes.`;
        } else {
          transcription = `[API Error] ${error.message}. Please check your API key and internet connection.`;
        }

        console.log('⚠️ Using fallback message');
        console.log('📝 Result:', transcription);
      }

      // Clean up the temporary audio file
      try {
        fs.unlinkSync(audioFilePath);
        console.log('Temporary audio file cleaned up');
      } catch (cleanupError) {
        console.error('Failed to clean up audio file:', cleanupError);
      }

      return {
        text: transcription.trim(),
        timestamp: new Date().toISOString(),
        model: options.model || 'whisper-1',
        language: options.language || 'auto'
      };

    } catch (error) {
      console.error('Transcription failed:', error);

      // Clean up the temporary audio file even on error
      try {
        if (fs.existsSync(audioFilePath)) {
          fs.unlinkSync(audioFilePath);
        }
      } catch (cleanupError) {
        console.error('Failed to clean up audio file after error:', cleanupError);
      }

      // Provide more specific error messages
      if (error.code === 'insufficient_quota') {
        throw new Error('OpenAI API quota exceeded. Please check your billing.');
      } else if (error.code === 'invalid_api_key') {
        throw new Error('Invalid OpenAI API key. Please check your settings.');
      } else if (error.code === 'model_not_found') {
        throw new Error('Selected model not available. Please try a different model.');
      } else {
        throw new Error(`Transcription failed: ${error.message}`);
      }
    }
  }

  async enhanceTranscription(text, options = {}) {
    if (!this.openai) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      console.log('Enhancing transcription with GPT...');
      
      const prompt = `Please improve the following transcription by adding proper punctuation, capitalization, and correcting any obvious errors while preserving the original meaning and tone. Do not add or remove content, only improve formatting and fix clear mistakes:

"${text}"

Improved version:`;

      const completion = await this.openai.chat.completions.create({
        model: options.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that improves transcriptions by adding proper punctuation and capitalization while preserving the original meaning.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      });

      const enhancedText = completion.choices[0].message.content.trim();
      console.log('Transcription enhanced:', enhancedText);
      
      return enhancedText;

    } catch (error) {
      console.error('Enhancement failed:', error);
      // Return original text if enhancement fails
      return text;
    }
  }

  async testConnection() {
    if (!this.openai) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      // Test the connection by listing available models
      const models = await this.openai.models.list();
      const whisperModels = models.data.filter(model => 
        model.id.includes('whisper')
      );
      
      return {
        connected: true,
        availableModels: whisperModels.map(model => model.id)
      };
      
    } catch (error) {
      console.error('Connection test failed:', error);
      return {
        connected: false,
        error: error.message
      };
    }
  }

  // Validate audio file format and size
  validateAudioFile(audioFilePath) {
    if (!fs.existsSync(audioFilePath)) {
      throw new Error('Audio file does not exist');
    }

    const stats = fs.statSync(audioFilePath);
    const fileSizeMB = stats.size / (1024 * 1024);
    
    // OpenAI has a 25MB limit for audio files
    if (fileSizeMB > 25) {
      throw new Error('Audio file is too large. Maximum size is 25MB.');
    }

    // Check file extension
    const allowedExtensions = ['.wav', '.mp3', '.m4a', '.ogg', '.flac'];
    const fileExtension = audioFilePath.toLowerCase().substring(audioFilePath.lastIndexOf('.'));
    
    if (!allowedExtensions.includes(fileExtension)) {
      throw new Error(`Unsupported audio format: ${fileExtension}. Supported formats: ${allowedExtensions.join(', ')}`);
    }

    return {
      valid: true,
      size: fileSizeMB,
      format: fileExtension
    };
  }
}

module.exports = TranscriptionService;
