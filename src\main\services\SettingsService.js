const Store = require('electron-store');

class SettingsService {
  constructor() {
    this.store = new Store({
      name: 'kambaavoice-settings',
      defaults: {
        // Global shortcut settings
        shortcut: 'CommandOrControl+Shift+V',
        
        // OpenAI settings
        apiKey: '********************************************************************************************************************************************************************',
        model: 'whisper-1',
        language: 'auto',
        enhanceWithGPT: false,
        
        // Recording settings
        timeout: 30,
        silenceThreshold: 0.5,
        audioFormat: 'wav',
        sampleRate: 16000,
        
        // Output settings
        autoType: true,
        typeDelay: 50,
        useClipboard: false,
        preserveFormatting: true,
        
        // UI settings
        showNotifications: true,
        minimizeToTray: true,
        startWithSystem: false,
        
        // History settings
        saveHistory: true,
        maxHistoryItems: 100,
        autoDeleteOldItems: true,
        
        // Advanced settings
        debugMode: false,
        logLevel: 'info'
      }
    });
  }

  // Get all settings
  getSettings() {
    return this.store.store;
  }

  // Get a specific setting
  getSetting(key) {
    return this.store.get(key);
  }

  // Set a specific setting
  setSetting(key, value) {
    this.store.set(key, value);
  }

  // Set multiple settings at once
  setSettings(settings) {
    Object.keys(settings).forEach(key => {
      this.store.set(key, settings[key]);
    });
  }

  // Reset settings to defaults
  resetSettings() {
    this.store.clear();
  }

  // Validate settings
  validateSettings(settings) {
    const errors = [];

    // Validate API key
    if (settings.apiKey && typeof settings.apiKey !== 'string') {
      errors.push('API key must be a string');
    }

    // Validate shortcut
    if (settings.shortcut && typeof settings.shortcut !== 'string') {
      errors.push('Shortcut must be a string');
    }

    // Validate timeout
    if (settings.timeout && (typeof settings.timeout !== 'number' || settings.timeout < 5 || settings.timeout > 300)) {
      errors.push('Timeout must be a number between 5 and 300 seconds');
    }

    // Validate type delay
    if (settings.typeDelay && (typeof settings.typeDelay !== 'number' || settings.typeDelay < 0 || settings.typeDelay > 1000)) {
      errors.push('Type delay must be a number between 0 and 1000 milliseconds');
    }

    // Validate model
    const validModels = ['whisper-1', 'whisper-1-large'];
    if (settings.model && !validModels.includes(settings.model)) {
      errors.push(`Model must be one of: ${validModels.join(', ')}`);
    }

    // Validate language
    const validLanguages = ['auto', 'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'];
    if (settings.language && !validLanguages.includes(settings.language)) {
      errors.push(`Language must be one of: ${validLanguages.join(', ')}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Export settings to file
  exportSettings() {
    return {
      settings: this.getSettings(),
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  // Import settings from file
  importSettings(settingsData) {
    if (!settingsData || !settingsData.settings) {
      throw new Error('Invalid settings data');
    }

    const validation = this.validateSettings(settingsData.settings);
    if (!validation.valid) {
      throw new Error(`Invalid settings: ${validation.errors.join(', ')}`);
    }

    this.setSettings(settingsData.settings);
    return true;
  }

  // Get settings for specific category
  getAudioSettings() {
    return {
      timeout: this.getSetting('timeout'),
      silenceThreshold: this.getSetting('silenceThreshold'),
      audioFormat: this.getSetting('audioFormat'),
      sampleRate: this.getSetting('sampleRate')
    };
  }

  getOpenAISettings() {
    return {
      apiKey: this.getSetting('apiKey'),
      model: this.getSetting('model'),
      language: this.getSetting('language'),
      enhanceWithGPT: this.getSetting('enhanceWithGPT')
    };
  }

  getTypingSettings() {
    return {
      autoType: this.getSetting('autoType'),
      typeDelay: this.getSetting('typeDelay'),
      useClipboard: this.getSetting('useClipboard'),
      preserveFormatting: this.getSetting('preserveFormatting')
    };
  }

  getUISettings() {
    return {
      showNotifications: this.getSetting('showNotifications'),
      minimizeToTray: this.getSetting('minimizeToTray'),
      startWithSystem: this.getSetting('startWithSystem')
    };
  }

  // Check if API key is configured
  isApiKeyConfigured() {
    const apiKey = this.getSetting('apiKey');
    return apiKey && apiKey.length > 0;
  }

  // Get masked API key for display
  getMaskedApiKey() {
    const apiKey = this.getSetting('apiKey');
    if (!apiKey || apiKey.length < 8) {
      return '';
    }
    return apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4);
  }

  // Update shortcut setting
  updateShortcut(newShortcut) {
    if (!newShortcut || typeof newShortcut !== 'string') {
      throw new Error('Invalid shortcut');
    }
    
    this.setSetting('shortcut', newShortcut);
    return true;
  }

  // Get file path for settings
  getSettingsPath() {
    return this.store.path;
  }

  // Check if settings file exists
  settingsExist() {
    return this.store.size > 0;
  }
}

module.exports = SettingsService;
